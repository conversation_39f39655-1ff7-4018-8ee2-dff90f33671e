"""
Prompts for feedback-enhanced gap analysis in Stage 2 conversion analysis.
"""
from typing import Dict, Any, List


def format_previous_attempts_for_prompt(previous_attempts: List[Dict]) -> str:
    """Format previous attempts for prompt following existing formatting patterns"""
    
    if not previous_attempts:
        return "No previous attempts available."
    
    formatted = "Previous Attempt History:\n"
    for i, attempt in enumerate(previous_attempts, 1):
        formatted += f"""
Attempt {i}:
- Strategy: {attempt.get('strategy_type', 'unknown')}
- Requirements: {len(attempt.get('requirements', []))}
- Result: {attempt.get('result_status', 'unknown')}
- Issues: {attempt.get('failure_reason', 'none')}
"""
    
    return formatted


def create_feedback_enhanced_gap_analysis_prompt(inputs: Dict[str, Any]) -> str:
    """
    Create feedback-enhanced analysis prompt for retry attempts with learning.
    
    This prompt performs enhanced transformation gap analysis based on AI comparison 
    feedback and previous attempt failures, providing targeted solutions.
    
    Args:
        inputs: Dictionary containing enhanced analysis context including:
            - current_output: Current incorrect output from modules
            - expected_output: Expected correct output from AI
            - original_source: Original source statement
            - deployment_error: Error context from deployment
            - responsible_modules: List of responsible modules
            - statement_number: Current statement number
            - attempt_number: Current attempt number (> 1)
            - ai_comparison_feedback: Feedback from AI comparison
            - previous_attempts: History of previous transformation attempts
            - migration_name: Migration name for database context
            - schema_name: Schema name
            - object_name: Object name
            - object_type: Object type
    
    Returns:
        Formatted prompt string for feedback-enhanced gap analysis
    """
    
    prompt = f"""You are performing ENHANCED transformation gap analysis based on AI comparison feedback and previous attempt failures.

FEEDBACK CONTEXT:
================
Attempt Number: {inputs['attempt_number']}
AI Comparison Feedback: {inputs['ai_comparison_feedback']}
Responsible Modules: {inputs['responsible_modules']}

CURRENT vs EXPECTED OUTPUT:
==========================
Current Output: {inputs['current_output']}
Expected Output: {inputs['expected_output']}

PREVIOUS ATTEMPT HISTORY:
========================
{format_previous_attempts_for_prompt(inputs.get('previous_attempts', []))}

ENHANCED ANALYSIS METHODOLOGY:
=============================

1. FEEDBACK INTERPRETATION:
   - Parse AI comparison feedback for specific transformation issues
   - Identify exact points of failure from previous attempts
   - Understand what the current approach is missing

2. FAILURE PATTERN ANALYSIS:
   - Review why previous transformation attempts failed
   - Identify patterns in unsuccessful approaches
   - Learn from attempt history to avoid repeating mistakes

3. REFINED TRANSFORMATION STRATEGY:
   - Develop targeted approach addressing specific feedback issues
   - Focus on precise transformation requirements
   - Provide enhanced implementation guidance

4. PRECISION TARGETING:
   - Address exact differences highlighted in AI feedback
   - Ensure new approach targets root causes, not symptoms
   - Validate against previous failure patterns

ENHANCED REQUIREMENTS OUTPUT:
============================
For each refined requirement, provide:

1. ADDRESSES_FEEDBACK_ISSUE: Specific feedback issue this requirement addresses
2. SPECIFIC_TRANSFORMATION: Exact change needed to fix the issue
3. IMPLEMENTATION_DETAILS: Detailed step-by-step implementation instructions
4. AVOIDS_PREVIOUS_FAILURE: How this approach avoids the previous failure pattern

ENHANCED STRATEGY OUTPUT:
========================
Provide an improved strategy that:
- Directly addresses the AI comparison feedback
- Learns from previous attempt failures
- Provides targeted solutions for specific issues
- Increases probability of success

SUCCESS PROBABILITY FACTORS:
===========================
Identify factors that increase success probability:
- Specific aspects that make this approach better than previous attempts
- Key differences from failed approaches
- Validation points to ensure success
- Risk mitigation strategies

CRITICAL FOCUS:
- Address specific issues mentioned in AI comparison feedback
- Avoid approaches that have already failed
- Provide precise, targeted transformation requirements
- Ensure high probability of success based on learning
- Make each requirement directly traceable to a specific feedback issue
"""
    
    return prompt
