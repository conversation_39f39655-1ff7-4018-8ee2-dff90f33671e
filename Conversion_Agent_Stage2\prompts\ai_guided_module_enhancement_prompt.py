"""
Prompts for AI-guided module enhancement in Stage 2 conversion analysis.
"""
from typing import Dict, Any
from Conversion_Agent_Stage2.prompts.module_enhancement_prompt import create_module_enhancement_prompt


def create_ai_guided_module_enhancement_prompt(
    context: Dict[str, Any],
    conversion_context: Dict[str, Any], 
    ai_comparison_feedback: str,
    attempt_history: list,
    migration_name: str,
    feature_name: str,
    keywords: str,
    db_terms: Dict[str, str],
    transformation_strategy: Dict[str, Any]
) -> str:
    """
    Create enhanced module enhancement prompt with AI transformation guidance.
    
    This function combines the existing module enhancement prompt with specific
    AI-driven transformation guidance to provide targeted enhancement instructions.
    
    Args:
        context: Module context including original code
        conversion_context: Conversion context with statements and errors
        ai_comparison_feedback: Feedback from AI comparison
        attempt_history: History of previous attempts
        migration_name: Migration name for database context
        feature_name: Name of the feature being enhanced
        keywords: Keywords associated with the feature
        db_terms: Database-specific terms
        transformation_strategy: AI transformation strategy with requirements
    
    Returns:
        Enhanced prompt string with AI guidance integration
    """
    
    # Get existing module enhancement prompt
    base_prompt = create_module_enhancement_prompt(
        original_module_code=context.get('original_module_code', ''),
        qmigrator_target_statement=conversion_context.get('original_target_statement', ''),
        ai_corrected_statement=conversion_context.get('ai_converted_statement', ''),
        deployment_error=conversion_context.get('original_deployment_error', ''),
        ai_comparison_feedback=ai_comparison_feedback,
        attempt_history=attempt_history or [],
        migration_name=migration_name,
        feature_name=feature_name,
        keywords=keywords,
        db_terms=db_terms
    )
    
    # Add AI guidance section following existing prompt structure
    ai_guidance_section = f"""

AI TRANSFORMATION GUIDANCE:
===========================
Analysis Type: {transformation_strategy['strategy_type']}
Summary: {transformation_strategy.get('analysis_summary', '')}

SPECIFIC TRANSFORMATION REQUIREMENTS:
====================================
"""
    
    requirements = transformation_strategy.get('requirements', [])
    for i, req in enumerate(requirements, 1):
        if transformation_strategy['strategy_type'] == 'comprehensive':
            ai_guidance_section += f"""
{i}. {req['description']}
   - Type: {req['transformation_type']}
   - Target Area: {req['target_area']}
   - Implementation: {req['implementation_guidance']}
   - Priority: {req['priority']}
"""
        else:  # feedback_enhanced
            ai_guidance_section += f"""
{i}. {req['specific_transformation']}
   - Addresses: {req['addresses_feedback_issue']}
   - Implementation: {req['implementation_details']}
   - Avoids: {req['avoids_previous_failure']}
"""
    
    ai_guidance_section += f"""

IMPLEMENTATION STRATEGY:
=======================
{transformation_strategy.get('implementation_strategy', '')}

VALIDATION CRITERIA:
===================
{transformation_strategy.get('validation_criteria', [])}

TRANSFORMATION SEQUENCE:
=======================
Apply the transformation requirements in this order:
1. HIGH priority requirements first (critical for success)
2. MEDIUM priority requirements second (important for correctness)
3. LOW priority requirements last (nice-to-have improvements)

Within each priority level, apply transformations in this sequence:
1. "removal" transformations (remove unwanted elements)
2. "replacement" transformations (replace incorrect syntax)
3. "addition" transformations (add missing elements)
4. "ordering" transformations (reorder elements for correct structure)

CRITICAL AI GUIDANCE INSTRUCTIONS:
==================================
This AI transformation guidance is specifically generated to address the current transformation gap.
You MUST follow these requirements to successfully transform the current output to the expected output.

IMPLEMENTATION PRIORITY:
1. FIRST: Address all HIGH priority transformation requirements
2. SECOND: Implement MEDIUM priority requirements  
3. THIRD: Consider LOW priority requirements if needed

TRANSFORMATION TYPE HANDLING:
- "ordering": Reorder elements/clauses to match expected structure
- "replacement": Replace specific syntax/keywords with target equivalents
- "addition": Add missing elements/clauses that appear in expected output
- "removal": Remove elements that shouldn't be in the final output

FEEDBACK-ENHANCED GUIDANCE (for retry attempts):
If this is a retry attempt, the guidance above specifically addresses why previous attempts failed.
Focus on the exact issues mentioned and implement the precise solutions provided.

Use this AI guidance to enhance the module while maintaining all existing functionality and following the generic approach principles.
"""
    
    # Combine base prompt with AI guidance (following existing prompt combination patterns)
    return base_prompt + ai_guidance_section
