"""
Prompts for comprehensive gap analysis in Stage 2 conversion analysis.
"""
from typing import Dict, Any
from Conversion_Agent_Stage2.utils.database_names import get_database_specific_terms


def create_comprehensive_gap_analysis_prompt(inputs: Dict[str, Any]) -> str:
    """
    Create comprehensive analysis prompt for identifying transformation requirements.
    
    This prompt analyzes the transformation gap between what modules currently produce
    versus what the AI expects, to provide specific guidance for module enhancement.

    Args:
        inputs: Dictionary containing analysis context including:
            - original_source: Original source statement (e.g., Oracle SQL)
            - current_target: What the current modules actually produced
            - expected_target: What the AI expects as correct target
            - deployment_error: Error context from deployment
            - responsible_modules: List of responsible modules
            - statement_number: Current statement number
            - attempt_number: Current attempt number
            - migration_name: Migration name for database context
            - schema_name: Schema name
            - object_name: Object name
            - object_type: Object type
    
    Returns:
        Formatted prompt string for comprehensive gap analysis
    """
    
    # Get database terms using existing utility
    db_terms = get_database_specific_terms(inputs['migration_name'])
    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    
    prompt = f"""You are an expert {source_db} to {target_db} transformation analyst. Analyze the differences between current and expected output to determine specific transformation requirements.

ANALYSIS CONTEXT:
================
Migration: {inputs['migration_name']}
Object: {inputs['schema_name']}.{inputs['object_name']} ({inputs['object_type']})
Statement Number: {inputs['statement_number']}
Responsible Modules: {inputs['responsible_modules']}

ORIGINAL SOURCE ({source_db}):
==============================
{inputs['original_source']}

CURRENT TARGET OUTPUT (What modules produced):
==============================================
{inputs['current_target']}

EXPECTED TARGET OUTPUT (What AI expects):
=========================================
{inputs['expected_target']}

DEPLOYMENT ERROR CONTEXT:
=========================
{inputs['deployment_error']}

TRANSFORMATION GAP ANALYSIS METHODOLOGY:
========================================

1. SOURCE-TO-TARGET TRANSFORMATION ANALYSIS:
   - Analyze how {source_db} source should transform to {target_db} target
   - Compare what modules currently produce vs what AI expects
   - Identify specific transformation gaps in the conversion logic

2. MODULE TRANSFORMATION DEFICIENCY ANALYSIS:
   - Determine why current modules produce incorrect target output
   - Identify missing transformation logic in responsible modules
   - Understand which {source_db} constructs are not properly converted

3. TRANSFORMATION REQUIREMENTS DEFINITION:
   - Define specific changes needed in modules to achieve expected target
   - Focus on source-to-target conversion patterns that are missing
   - Provide implementation guidance for enhancing transformation logic

4. MODULE ENHANCEMENT STRATEGY:
   - Determine how to enhance modules to handle the source constructs correctly
   - Specify transformation logic that needs to be added or modified
   - Provide validation criteria for successful source-to-target conversion

TRANSFORMATION REQUIREMENTS OUTPUT:
===================================
For each transformation requirement, provide:

1. DESCRIPTION: Clear description of what needs to be transformed
2. TRANSFORMATION_TYPE: One of: "ordering", "replacement", "addition", "removal"
3. IMPLEMENTATION_GUIDANCE: Specific step-by-step instructions for the module enhancement
4. TARGET_AREA: Which part of the code/output needs modification
5. PRIORITY: "high", "medium", or "low" based on impact on achieving expected output

IMPLEMENTATION STRATEGY OUTPUT:
==============================
Provide a clear strategy that explains:
- The overall approach for module enhancement
- The sequence of transformations to apply
- How to preserve existing functionality while adding new logic
- Specific patterns or techniques to use

VALIDATION CRITERIA OUTPUT:
===========================
Provide specific criteria to validate success:
- Exact output format expectations
- Key elements that must be present/absent
- Structural requirements that must be met
- Error conditions that must be avoided

CRITICAL INSTRUCTIONS:
- Focus on achieving the exact expected output
- Consider the deployment error as a key constraint
- Provide specific, implementable transformation guidance
- Ensure compatibility with existing module enhancement patterns
- Make requirements actionable and specific, not generic
"""
    
    return prompt
