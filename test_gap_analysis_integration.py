#!/usr/bin/env python3
"""
Test script to verify Gap Analysis integration with Stage 2 workflow.

This script tests the key components of the AI-driven transformation gap analysis
implementation to ensure proper integration with existing Stage 2 workflow.
"""

import sys
import os
sys.path.append('.')

def test_schema_imports():
    """Test that all new schemas can be imported successfully."""
    print("🧪 Testing schema imports...")
    
    try:
        from Conversion_Agent_Stage2.state import (
            GapAnalysisSchema, 
            EnhancedGapAnalysisSchema,
            TransformationRequirement,
            EnhancedTransformationRequirement
        )
        print("✅ All schemas imported successfully")
        return True
    except Exception as e:
        print(f"❌ Schema import failed: {e}")
        return False

def test_schema_creation():
    """Test that schemas can be created with valid data."""
    print("🧪 Testing schema creation...")
    
    try:
        from Conversion_Agent_Stage2.state import (
            GapAnalysisSchema, 
            TransformationRequirement
        )
        
        # Test TransformationRequirement
        req = TransformationRequirement(
            description="Test transformation",
            transformation_type="replacement",
            implementation_guidance="Test guidance",
            target_area="Test area",
            priority="high"
        )
        
        # Test GapAnalysisSchema
        schema = GapAnalysisSchema(
            analysis_summary="Test analysis summary",
            root_causes=["Test root cause"],
            transformation_requirements=[req],
            implementation_strategy="Test strategy",
            validation_criteria=["Test criteria"]
        )
        
        print("✅ Schema creation successful")
        print(f"   📋 Analysis summary: {schema.analysis_summary}")
        print(f"   🔧 Requirements count: {len(schema.transformation_requirements)}")
        return True
        
    except Exception as e:
        print(f"❌ Schema creation failed: {e}")
        return False

def test_node_methods():
    """Test that new node methods exist and are callable."""
    print("🧪 Testing node methods...")
    
    try:
        from Conversion_Agent_Stage2.nodes.conversion_nodes import Stage2ProcessingNodes
        
        # Create nodes instance (with None LLM for testing)
        nodes = Stage2ProcessingNodes(None)
        
        # Test method existence
        methods_to_test = [
            'analyze_transformation_gap',
            'collect_gap_analysis_inputs',
            'perform_comprehensive_gap_analysis',
            'perform_feedback_enhanced_gap_analysis',
            'create_comprehensive_gap_analysis_prompt',
            'create_feedback_enhanced_gap_analysis_prompt',
            'process_gap_analysis_results',
            'log_gap_analysis_to_excel',
            'store_attempt_in_history',
            'create_ai_guided_module_enhancement_prompt'
        ]
        
        missing_methods = []
        for method_name in methods_to_test:
            if not hasattr(nodes, method_name):
                missing_methods.append(method_name)
        
        if missing_methods:
            print(f"❌ Missing methods: {missing_methods}")
            return False
        
        print("✅ All node methods exist")
        print(f"   🔧 Methods tested: {len(methods_to_test)}")
        return True
        
    except Exception as e:
        print(f"❌ Node method testing failed: {e}")
        return False

def test_workflow_integration():
    """Test that workflow graph includes the new node."""
    print("🧪 Testing workflow integration...")
    
    try:
        from Conversion_Agent_Stage2.workflow.graph_builder import Stage2GraphBuilder
        
        # Create graph builder (with None LLM for testing)
        builder = Stage2GraphBuilder(None)
        
        # Build the graph to initialize conversion_nodes
        builder.build_graph()

        # Check if the graph builder has the conversion nodes
        if hasattr(builder, 'conversion_nodes'):
            nodes = builder.conversion_nodes
            if hasattr(nodes, 'analyze_transformation_gap'):
                print("✅ Workflow integration successful")
                print("   🔗 Gap analysis node available in workflow")
                return True
            else:
                print("❌ Gap analysis method not found in workflow nodes")
                return False
        else:
            print("❌ Conversion nodes not found in graph builder")
            return False
        
    except Exception as e:
        print(f"❌ Workflow integration testing failed: {e}")
        return False

def test_prompt_enhancement():
    """Test that AI guidance can be integrated with existing prompts."""
    print("🧪 Testing prompt enhancement...")
    
    try:
        from Conversion_Agent_Stage2.prompts.module_enhancement_prompt import create_module_enhancement_prompt
        
        # Test basic prompt creation
        base_prompt = create_module_enhancement_prompt(
            original_module_code="def test(): pass",
            qmigrator_target_statement="SELECT * FROM test",
            ai_corrected_statement="SELECT * FROM test_table",
            deployment_error="Test error",
            migration_name="Oracle_Postgres14"
        )
        
        if base_prompt and len(base_prompt) > 100:
            print("✅ Prompt enhancement integration successful")
            print(f"   📝 Base prompt length: {len(base_prompt)} characters")
            return True
        else:
            print("❌ Prompt creation failed or too short")
            return False
        
    except Exception as e:
        print(f"❌ Prompt enhancement testing failed: {e}")
        return False

def run_all_tests():
    """Run all integration tests."""
    print("🚀 Starting Gap Analysis Integration Tests")
    print("=" * 60)
    
    tests = [
        ("Schema Imports", test_schema_imports),
        ("Schema Creation", test_schema_creation),
        ("Node Methods", test_node_methods),
        ("Workflow Integration", test_workflow_integration),
        ("Prompt Enhancement", test_prompt_enhancement)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)
        result = test_func()
        results.append((test_name, result))
    
    print("\n" + "=" * 60)
    print("🏁 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:.<30} {status}")
        if result:
            passed += 1
        else:
            failed += 1
    
    print("-" * 60)
    print(f"Total Tests: {len(results)}")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! Gap Analysis integration is ready!")
        return True
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
